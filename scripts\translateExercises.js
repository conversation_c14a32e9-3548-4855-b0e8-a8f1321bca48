const fs = require('fs').promises;
const path = require('path');

// Translation mappings
const translations = {
  // Exercise names
  "Hít đất": "Push Up",
  "Kéo xà": "Pull Up",
  "Squat": "Squat",
  "Plank": "Plank",
  "Burpee": "Burpee",
  "Bicep Curl": "Bicep Curl",
  
  // Muscle group names
  "Ngực": "Chest",
  "Lưng": "Back",
  "Vai": "Shoulders",
  "Tay": "Arms",
  "Chân": "Legs",
  "Bụng": "Abs",
  "Toàn thân": "Full Body",
  
  // Equipment
  "Xà đơn": "Pull-up bar",
  "Tạ đơn": "Dumbbells",
  "Thanh tạ": "Barbell",
  "Máy tập": "Machine",
  "Dây kháng lực": "Resistance band",
  "Bóng tập": "Exercise ball",
  "G<PERSON><PERSON> tập": "Bench",
  
  // Common instruction phrases
  "Nằm sấp": "Lie face down",
  "Đứng thẳng": "Stand straight",
  "Treo người": "Hang",
  "<PERSON><PERSON> thấp": "Lower",
  "Đ<PERSON><PERSON> mạnh": "Push up",
  "Kéo lên": "Pull up",
  "Duỗi thẳng": "Extend straight",
  "Giữ lưng thẳng": "Keep back straight",
  "Tập trung": "Focus on",
  "Kiểm soát": "Control",
  "Lặp lại": "Repeat",
  "về vị trí ban đầu": "to starting position",
  "từ từ": "slowly",
  "cho đến khi": "until",
  "rộng bằng vai": "shoulder-width apart",
  
  // Tips and mistakes
  "Giữ cơ thể thẳng": "Keep body straight",
  "Không để": "Don't let",
  "quá cao": "too high",
  "quá thấp": "too low",
  "không đúng cách": "incorrectly",
  "Sử dụng quá nhiều": "Using too much",
  "động lực": "momentum",
  "Không kéo đủ cao": "Not pulling high enough",
  "quá nhanh": "too fast",
  
  // Breathing
  "Hít vào": "Inhale",
  "Thở ra": "Exhale",
  "khi hạ xuống": "when lowering",
  "khi đẩy lên": "when pushing up",
  "khi kéo lên": "when pulling up",
  "Thở đều": "Breathe steadily",
  "trong suốt bài tập": "throughout the exercise",
  
  // Time units
  "giây": "seconds",
  "phút": "minutes",
  
  // General terms
  "Bài tập": "Exercise",
  "cơ bản": "basic",
  "giúp phát triển": "helps develop",
  "phát triển": "develops",
  "cơ": "muscle",
  "và": "and",
  "hoặc": "or",
  "của": "of",
  "cho": "for",
  "với": "with",
  "trong": "in",
  "trên": "on",
  "dưới": "under",
  "sau": "after",
  "trước": "before"
};

// More comprehensive translation function
function translateText(text) {
  if (!text || typeof text !== 'string') return text;

  let translated = text;

  // Handle specific complete phrases first
  const phraseTranslations = {
    "Bài tập cơ bản giúp phát triển cơ ngực, vai và tay sau": "Basic exercise that helps develop chest, shoulder and tricep muscles",
    "Bài tập phát triển cơ lưng, vai sau và cơ tay trước": "Exercise that develops back, rear shoulder and bicep muscles",
    "Bài tập cơ bản cho cơ đùi, mông và cơ chân": "Basic exercise for thigh, glute and leg muscles",
    "Nằm sấp, đặt hai tay xuống sàn rộng bằng vai": "Lie face down, place hands on floor shoulder-width apart",
    "Duỗi thẳng cơ thể từ đầu đến chân": "Keep body straight from head to feet",
    "Hạ thấp cơ thể cho đến khi ngực gần chạm sàn": "Lower body until chest nearly touches the floor",
    "Đẩy mạnh để trở về vị trí ban đầu": "Push up forcefully to return to starting position",
    "Treo người trên xà đơn, tay rộng bằng vai": "Hang from pull-up bar with hands shoulder-width apart",
    "Kéo cơ thể lên cho đến khi cằm vượt qua xà": "Pull body up until chin passes over the bar",
    "Hạ xuống từ từ về vị trí ban đầu": "Lower slowly to starting position",
    "Đứng thẳng, chân rộng bằng vai": "Stand straight with feet shoulder-width apart",
    "Hạ thấp cơ thể như ngồi xuống ghế": "Lower body as if sitting down on a chair",
    "Giữ lưng thẳng, đầu gối không vượt qua mũi chân": "Keep back straight, knees don't go past toes",
    "Đứng lên về vị trí ban đầu": "Stand up to starting position",
    "Giữ cơ thể thẳng như một đường thẳng": "Keep body straight like a plank",
    "Không để hông quá cao hoặc quá thấp": "Don't let hips go too high or too low",
    "Tập trung vào việc siết chặt cơ bụng": "Focus on engaging core muscles",
    "Bắt đầu từ vị trí treo hoàn toàn": "Start from full hanging position",
    "Kéo bằng cơ lưng, không chỉ bằng tay": "Pull with back muscles, not just arms",
    "Kiểm soát tốc độ khi hạ xuống": "Control the speed when lowering",
    "Giữ trọng lượng ở gót chân": "Keep weight on heels",
    "Đẩy đầu gối ra ngoài": "Push knees outward",
    "Giữ ngực thẳng": "Keep chest up",
    "Hít vào khi hạ xuống, thở ra khi đẩy lên": "Inhale when lowering, exhale when pushing up",
    "Thở ra khi kéo lên, hít vào khi hạ xuống": "Exhale when pulling up, inhale when lowering",
    "Thở đều trong suốt bài tập": "Breathe steadily throughout the exercise"
  };

  // Apply phrase translations first
  for (const [vietnamese, english] of Object.entries(phraseTranslations)) {
    translated = translated.replace(new RegExp(vietnamese, 'gi'), english);
  }

  // Then apply word-level translations for remaining text
  for (const [vietnamese, english] of Object.entries(translations)) {
    const regex = new RegExp(`\\b${vietnamese}\\b`, 'gi');
    translated = translated.replace(regex, english);
  }

  // Handle specific patterns that weren't caught
  translated = translated
    .replace(/Bài tập (.+) giúp phát triển cơ (.+)/gi, '$1 exercise helps develop $2 muscles')
    .replace(/Chuẩn bị tư thế ban đầu cho bài tập (.+)/gi, 'Prepare starting position for $1 exercise')
    .replace(/Thực hiện động tác theo đúng kỹ thuật/gi, 'Perform the movement with proper technique')
    .replace(/Tập trung vào nhóm cơ (.+)/gi, 'Focus on $1 muscle group')
    .replace(/Kiểm soát tốc độ và trở về vị trí ban đầu/gi, 'Control the speed and return to starting position')
    .replace(/Giữ tư thế đúng trong suốt bài tập/gi, 'Maintain proper posture throughout the exercise')
    .replace(/Kiểm soát tốc độ thực hiện/gi, 'Control the execution speed')
    .replace(/Tập trung vào cơ đích/gi, 'Focus on target muscles')
    .replace(/Thực hiện quá nhanh/gi, 'Performing too fast')
    .replace(/Không giữ tư thế đúng/gi, 'Not maintaining proper posture')
    .replace(/Sử dụng trọng lượng không phù hợp/gi, 'Using inappropriate weight');

  return translated;
}

async function translateExercises() {
  try {
    console.log('Loading exercises.json...');
    const exercisesPath = path.join(__dirname, '../src/data/exercises.json');
    const data = await fs.readFile(exercisesPath, 'utf8');
    const exercises = JSON.parse(data);
    
    console.log(`Found ${exercises.length} exercises to translate`);
    
    const translatedExercises = exercises.map((exercise, index) => {
      console.log(`Translating exercise ${index + 1}/${exercises.length}: ${exercise.name}`);
      
      return {
        ...exercise,
        name: translateText(exercise.name),
        muscleGroup: {
          ...exercise.muscleGroup,
          name: translateText(exercise.muscleGroup.name)
        },
        description: translateText(exercise.description),
        instructions: exercise.instructions.map(instruction => translateText(instruction)),
        tips: exercise.tips ? exercise.tips.map(tip => translateText(tip)) : [],
        commonMistakes: exercise.commonMistakes ? exercise.commonMistakes.map(mistake => translateText(mistake)) : [],
        breathingTechnique: exercise.breathingTechnique ? translateText(exercise.breathingTechnique) : '',
        equipment: exercise.equipment ? exercise.equipment.map(eq => translateText(eq)) : []
      };
    });
    
    // Save translated exercises
    const outputPath = path.join(__dirname, '../src/data/exercises_translated.json');
    await fs.writeFile(outputPath, JSON.stringify(translatedExercises, null, 2), 'utf8');
    
    console.log(`✓ Successfully translated and saved ${translatedExercises.length} exercises to exercises_translated.json`);
    console.log('Please review the translations and then replace the original exercises.json file');
    
  } catch (error) {
    console.error('Error translating exercises:', error);
  }
}

// Run the translation
translateExercises();
