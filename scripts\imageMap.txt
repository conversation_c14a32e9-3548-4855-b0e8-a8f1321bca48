const imageMap: Record<string, any> = {
  '45_degree_incline_row.gif': require('../assets/images/45_degree_incline_row.gif'),
  'alternate_leg_raises.gif': require('../assets/images/alternate_leg_raises.gif'),
  'arm_circles.gif': require('../assets/images/arm_circles.gif'),
  'arm_scissors.gif': require('../assets/images/arm_scissors.gif'),
  'barbell_jm_press.gif': require('../assets/images/barbell_jm_press.gif'),
  'barbell_military_press_overhead_press.gif': require('../assets/images/barbell_military_press_overhead_press.gif'),
  'bench_press.gif': require('../assets/images/bench_press.gif'),
  'bent_over_dumbbell_row.gif': require('../assets/images/bent_over_dumbbell_row.gif'),
  'bicep_curl.gif': require('../assets/images/bicep_curl.gif'),
  'bicycle_crunch.gif': require('../assets/images/bicycle_crunch.gif'),
  'bodyweight_lunges.gif': require('../assets/images/bodyweight_lunges.gif'),
  'bodyweight_plie_squat.gif': require('../assets/images/bodyweight_plie_squat.gif'),
  'burpee.gif': require('../assets/images/burpee.gif'),
  'cable_crossover.gif': require('../assets/images/cable_crossover.gif'),
  'cable_lateral_raise.gif': require('../assets/images/cable_lateral_raise.gif'),
  'cable_rear_pulldown.gif': require('../assets/images/cable_rear_pulldown.gif'),
  'cable_tricep_kickback.gif': require('../assets/images/cable_tricep_kickback.gif'),
  'crunches.gif': require('../assets/images/crunches.gif'),
  'curtsy_lunge.gif': require('../assets/images/curtsy_lunge.gif'),
  'deadlift.gif': require('../assets/images/deadlift.gif'),
  'depth_jump_to_hurdle_hop.gif': require('../assets/images/depth_jump_to_hurdle_hop.gif'),
  'dips.gif': require('../assets/images/dips.gif'),
  'double_arm_dumbbell_curl.gif': require('../assets/images/double_arm_dumbbell_curl.gif'),
  'dragon_flag.gif': require('../assets/images/dragon_flag.gif'),
  'dumbbell_4_way_lateral_raise.gif': require('../assets/images/dumbbell_4_way_lateral_raise.gif'),
  'dumbbell_6_way_raise.gif': require('../assets/images/dumbbell_6_way_raise.gif'),
  'dumbbell_bench_press.gif': require('../assets/images/dumbbell_bench_press.gif'),
  'dumbbell_bent_over_reverse_grip_row.gif': require('../assets/images/dumbbell_bent_over_reverse_grip_row.gif'),
  'dumbbell_chest_supported_lateral_raises.gif': require('../assets/images/dumbbell_chest_supported_lateral_raises.gif'),
  'dumbbell_cossack_squat.gif': require('../assets/images/dumbbell_cossack_squat.gif'),
  'dumbbell_curl.gif': require('../assets/images/dumbbell_curl.gif'),
  'dumbbell_deadlift.gif': require('../assets/images/dumbbell_deadlift.gif'),
  'dumbbell_front_raise.gif': require('../assets/images/dumbbell_front_raise.gif'),
  'dumbbell_goblet_squat.gif': require('../assets/images/dumbbell_goblet_squat.gif'),
  'dumbbell_good_morning.gif': require('../assets/images/dumbbell_good_morning.gif'),
  'dumbbell_kickback.gif': require('../assets/images/dumbbell_kickback.gif'),
  'dumbbell_lateral_raise.gif': require('../assets/images/dumbbell_lateral_raise.gif'),
  'dumbbell_pullover.gif': require('../assets/images/dumbbell_pullover.gif'),
  'dumbbell_push_press.gif': require('../assets/images/dumbbell_push_press.gif'),
  'dumbbell_row.gif': require('../assets/images/dumbbell_row.gif'),
  'dumbbell_seated_front_and_back_tate_press.gif': require('../assets/images/dumbbell_seated_front_and_back_tate_press.gif'),
  'dumbbell_shoulder_press.gif': require('../assets/images/dumbbell_shoulder_press.gif'),
  'dumbbell_squat.gif': require('../assets/images/dumbbell_squat.gif'),
  'full_planche.gif': require('../assets/images/full_planche.gif'),
  'half_arnold_press.gif': require('../assets/images/half_arnold_press.gif'),
  'high_cable_single_arm_bicep_curl.gif': require('../assets/images/high_cable_single_arm_bicep_curl.gif'),
  'incline_chest_fly_machine.gif': require('../assets/images/incline_chest_fly_machine.gif'),
  'incline_dumbbell_fly.gif': require('../assets/images/incline_dumbbell_fly.gif'),
  'lat_pulldown.gif': require('../assets/images/lat_pulldown.gif'),
  'leaning_single_arm_dumbbell_lateral_raise.gif': require('../assets/images/leaning_single_arm_dumbbell_lateral_raise.gif'),
  'lever_front_pulldown.gif': require('../assets/images/lever_front_pulldown.gif'),
  'lever_preacher_curl.gif': require('../assets/images/lever_preacher_curl.gif'),
  'lever_shoulder_press.gif': require('../assets/images/lever_shoulder_press.gif'),
  'lever_triceps_dip.gif': require('../assets/images/lever_triceps_dip.gif'),
  'lying_barbell_triceps_extension.gif': require('../assets/images/lying_barbell_triceps_extension.gif'),
  'lying_cable_curl.gif': require('../assets/images/lying_cable_curl.gif'),
  'lying_scissor_kick.gif': require('../assets/images/lying_scissor_kick.gif'),
  'medicine_ball_rotational_throw.gif': require('../assets/images/medicine_ball_rotational_throw.gif'),
  'mountain_climber.gif': require('../assets/images/mountain_climber.gif'),
  'muscleup.gif': require('../assets/images/muscleup.gif'),
  'onearm_cable_chest_press.gif': require('../assets/images/onearm_cable_chest_press.gif'),
  'onearm_reverse_pushdown.gif': require('../assets/images/onearm_reverse_pushdown.gif'),
  'onearm_singleleg_bench_dips.gif': require('../assets/images/onearm_singleleg_bench_dips.gif'),
  'one_arm_cable_curl.gif': require('../assets/images/one_arm_cable_curl.gif'),
  'one_arm_triceps_pushdown.gif': require('../assets/images/one_arm_triceps_pushdown.gif'),
  'pec_deck_fly.gif': require('../assets/images/pec_deck_fly.gif'),
  'plank.gif': require('../assets/images/plank.gif'),
  'power_lunge.gif': require('../assets/images/power_lunge.gif'),
  'pullup.gif': require('../assets/images/pullup.gif'),
  'pull_up.gif': require('../assets/images/pull_up.gif'),
  'push_up.gif': require('../assets/images/push_up.gif'),
  'rear_delt_fly_machine.gif': require('../assets/images/rear_delt_fly_machine.gif'),
  'reverse_latpulldown.gif': require('../assets/images/reverse_latpulldown.gif'),
  'rowing_machine.gif': require('../assets/images/rowing_machine.gif'),
  'russian_twist.gif': require('../assets/images/russian_twist.gif'),
  'seated_barbell_shoulder_press.gif': require('../assets/images/seated_barbell_shoulder_press.gif'),
  'seated_behind_neck_press.gif': require('../assets/images/seated_behind_neck_press.gif'),
  'seated_incline_dumbbell_curl.gif': require('../assets/images/seated_incline_dumbbell_curl.gif'),
  'seated_rear_lateral_dumbbell_raise.gif': require('../assets/images/seated_rear_lateral_dumbbell_raise.gif'),
  'seated_zottman_curl.gif': require('../assets/images/seated_zottman_curl.gif'),
  'shoulder_press.gif': require('../assets/images/shoulder_press.gif'),
  'side_plank_hip_adduction.gif': require('../assets/images/side_plank_hip_adduction.gif'),
  'singlearm_cable_crossover.gif': require('../assets/images/singlearm_cable_crossover.gif'),
  'smith_machine_squat.gif': require('../assets/images/smith_machine_squat.gif'),
  'squat.gif': require('../assets/images/squat.gif'),
  'standing_barbell_concentration_curl.gif': require('../assets/images/standing_barbell_concentration_curl.gif'),
  'standing_close_grip_military_press.gif': require('../assets/images/standing_close_grip_military_press.gif'),
  'standing_dumbbell_shoulder_press.gif': require('../assets/images/standing_dumbbell_shoulder_press.gif'),
  'triceps_dips_on_floor.gif': require('../assets/images/triceps_dips_on_floor.gif'),
  'two_arm_dumbbell_front_raise.gif': require('../assets/images/two_arm_dumbbell_front_raise.gif'),
  'waiter_curl.gif': require('../assets/images/waiter_curl.gif'),
};