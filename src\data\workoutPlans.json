[{"id": "beginner_7_day", "name": "7-Day Beginner Plan", "description": "Basic workout program for beginners", "duration": 7, "goal": "maintenance", "difficulty": "<PERSON><PERSON><PERSON>", "workouts": [{"day": 1, "name": "Day 1: Basic Full Body", "exercises": [{"exerciseId": "push_up", "sets": 2, "reps": "8-10", "restTime": 60}, {"exerciseId": "squat", "sets": 2, "reps": "10-12", "restTime": 60}, {"exerciseId": "plank", "sets": 2, "reps": "20-30 seconds", "restTime": 45}], "estimatedTime": 20}, {"day": 2, "name": "Day 2: Rest or Light Walk", "exercises": [], "estimatedTime": 0}, {"day": 3, "name": "Day 3: Leg <PERSON>", "exercises": [{"exerciseId": "squat", "sets": 3, "reps": "12-15", "restTime": 60}], "estimatedTime": 15}, {"day": 4, "name": "Day 4: Rest", "exercises": [], "estimatedTime": 0}, {"day": 5, "name": "Day 5: Upper Body Focus", "exercises": [{"exerciseId": "push_up", "sets": 3, "reps": "8-12", "restTime": 60}, {"exerciseId": "plank", "sets": 3, "reps": "30-45 seconds", "restTime": 45}], "estimatedTime": 20}, {"day": 6, "name": "Day 6: Light Full Body", "exercises": [{"exerciseId": "burpee", "sets": 2, "reps": "3-5", "restTime": 90}], "estimatedTime": 15}, {"day": 7, "name": "Day 7: Complete Rest", "exercises": [], "estimatedTime": 0}]}, {"id": "muscle_gain_30_day", "name": "30-Day <PERSON><PERSON><PERSON>", "description": "Muscle building workout program for 30 days", "duration": 30, "goal": "muscle_gain", "difficulty": "Intermediate", "workouts": [{"day": 1, "name": "Day 1: Chest & Triceps", "exercises": [{"exerciseId": "push_up", "sets": 4, "reps": "12-15", "restTime": 75}], "estimatedTime": 45}, {"day": 2, "name": "Day 2: Back & Biceps", "exercises": [{"exerciseId": "pull_up", "sets": 4, "reps": "6-10", "restTime": 90}, {"exerciseId": "bicep_curl", "sets": 3, "reps": "12-15", "restTime": 60}], "estimatedTime": 50}, {"day": 3, "name": "Day 3: Le<PERSON> & Glutes", "exercises": [{"exerciseId": "squat", "sets": 4, "reps": "15-20", "restTime": 75}], "estimatedTime": 40}]}]