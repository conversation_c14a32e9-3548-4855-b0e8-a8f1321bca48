# 🏋️ Ứng dụng Hướng dẫn Tập Gym

Ứng dụng React Native hoàn chỉnh để hướng dẫn tập luyện gym với đầy đủ các tính năng quản lý bài tập, lộ trình tập luy<PERSON>, và theo dõi tiến trình.

## ✨ Tính năng chính

### 🏠 Trang chủ (Home)
- Hiển thị gợi ý bài tập hôm nay
- Thống kê nhanh về số lượng bài tập, y<PERSON><PERSON> thích, buổi tập
- Thao tác nhanh: Hẹn giờ, <PERSON><PERSON> trình, <PERSON><PERSON><PERSON> tập, Tiến trình
- Giao diện thân thiện với hiệu ứng động

### 💪 Danh sách bài tập theo nhóm cơ
- Các nhóm cơ: <PERSON>ự<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, To<PERSON><PERSON> thân
- Mỗi nhóm cơ có nhiều bài tập chi tiết
- Dữ liệu được lưu trong file JSON nội bộ
- Hình ảnh thật cho từng bài tập

### 📋 Chi tiết bài tập
- Hình ảnh minh họa chất lượng cao
- Mô tả chi tiết: số rep, set, thời gian nghỉ
- Hướng dẫn kỹ thuật thở đúng cách
- Mẹo hay và lỗi thường gặp
- Danh sách dụng cụ cần thiết

### 📅 Lộ trình tập (Workout Plans)
- Lộ trình 7 ngày cho người mới bắt đầu
- Lộ trình 30 ngày tăng cơ
- Phân loại theo mục tiêu: tăng cơ, giảm mỡ, duy trì sức khỏe
- Lưu lịch sử tiến trình vào AsyncStorage

### 🗓️ Lịch tập (Calendar)
- Cho phép người dùng chọn bài tập cho từng ngày
- Đánh dấu đã hoàn thành bài tập
- Thống kê theo tháng
- Giao diện lịch trực quan

### ⏱️ Bộ đếm thời gian (Timer)
- Hẹn giờ nghỉ giữa các set: 30s, 60s, 90s, 2 phút
- Hẹn giờ tùy chỉnh
- Âm thanh và thông báo cảnh báo
- Giao diện đẹp mắt với thanh tiến trình

### 📊 Ghi chú tiến trình tập luyện
- Ghi lại cân nặng, tỷ lệ mỡ
- Ghi chú cảm nhận hàng ngày
- Thống kê buổi tập
- Hệ thống thành tích (achievements)

### ❤️ Bài tập yêu thích
- Thêm/xóa bài tập yêu thích bằng biểu tượng trái tim
- Nhóm theo nhóm cơ
- Tạo lộ trình từ bài tập yêu thích
- Chọn bài tập ngẫu nhiên

### 🔍 Tìm kiếm bài tập
- Tìm theo tên bài tập
- Lọc theo nhóm cơ, độ khó, dụng cụ
- Lịch sử tìm kiếm
- Gợi ý tìm kiếm phổ biến

## 🛠️ Công nghệ sử dụng

- **React Native 0.77.1** - Framework chính
- **TypeScript** - Type safety
- **Zustand** - Quản lý state toàn cục
- **React Navigation 6** - Điều hướng
- **AsyncStorage** - Lưu trữ dữ liệu local
- **React Native Vector Icons** - Biểu tượng
- **React Native Reanimated** - Animation

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js >= 18
- React Native CLI
- Android Studio (cho Android)
- Xcode (cho iOS)

### Bước 1: Clone và cài đặt dependencies
```bash
git clone <repository-url>
cd react-native-gym-app
npm install
```

### Bước 2: Cài đặt pods cho iOS
```bash
cd ios && pod install && cd ..
```

### Bước 3: Chạy Metro bundler
```bash
npm start
```

### Bước 4: Chạy ứng dụng

#### Android
```bash
npm run android
```

#### iOS
```bash
npm run ios
```

## 📁 Cấu trúc dự án

```
src/
├── components/          # Components tái sử dụng
│   ├── ExerciseCard.tsx
│   ├── MuscleGroupCard.tsx
│   └── TimerComponent.tsx
├── data/               # Dữ liệu JSON
│   ├── exercises.json
│   ├── muscleGroups.json
│   └── workoutPlans.json
├── navigators/         # Navigation setup
├── screens/           # Các màn hình
│   ├── HomeScreen.tsx
│   ├── ExerciseListScreen.tsx
│   ├── ExerciseDetailScreen.tsx
│   ├── TimerScreen.tsx
│   ├── FavoritesScreen.tsx
│   ├── SearchScreen.tsx
│   ├── WorkoutPlansScreen.tsx
│   ├── CalendarScreen.tsx
│   └── ProgressScreen.tsx
├── store/             # Zustand stores
│   ├── gymStore.ts
│   └── timerStore.ts
└── types/             # TypeScript types
    └── gym.types.ts
```

## 🎯 Tính năng nổi bật

### State Management với Zustand
- Quản lý state đơn giản và hiệu quả
- Persist data với AsyncStorage
- Type-safe với TypeScript

### Dữ liệu thực tế
- Hình ảnh bài tập thật từ Unsplash
- Hướng dẫn chi tiết và khoa học
- Dữ liệu có thể mở rộng dễ dàng

### UX/UI tối ưu
- Giao diện Material Design
- Animation mượt mà
- Responsive design

## 🔧 Tùy chỉnh

### Thêm bài tập mới
Chỉnh sửa file `src/data/exercises.json`

### Thêm lộ trình mới
Chỉnh sửa file `src/data/workoutPlans.json`

---

**Lưu ý**: Ứng dụng này được tạo ra cho mục đích học tập và demo. Hãy tham khảo ý kiến chuyên gia trước khi thực hiện các bài tập.