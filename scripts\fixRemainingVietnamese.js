const fs = require('fs').promises;
const path = require('path');

// Additional translations for remaining Vietnamese text
const additionalTranslations = {
  // Common mistakes
  "Để hông": "Letting hips",
  "Không Lower": "Not lowering",
  "Thở": "Breathing",
  "Sử dụng trọng lượng quá nặng": "Using too heavy weight",
  "Thực hiện": "Performing",
  "khi đứng lên": "when standing up",
  
  // Mixed phrases that need fixing
  "too high": "too high",
  "đủ": "enough",
  "incorrectly": "incorrectly",
  "too fast": "too fast",
  "when lowering": "when lowering",
  "when pushing up": "when pushing up",
  "when pulling up": "when pulling up",
  "Exhale": "Exhale",
  "Inhale": "Inhale"
};

function cleanupTranslation(text) {
  if (!text || typeof text !== 'string') return text;
  
  let cleaned = text;
  
  // Fix specific problematic patterns
  cleaned = cleaned
    .replace(/Để hông too high/gi, "Letting hips go too high")
    .replace(/Không Lower đủ/gi, "Not lowering enough")
    .replace(/Thở incorrectly/gi, "Breathing incorrectly")
    .replace(/Sử dụng trọng lượng quá nặng/gi, "Using too heavy weight")
    .replace(/Thực hiện too fast/gi, "Performing too fast")
    .replace(/Exhale khi đứng lên/gi, "Exhale when standing up")
    .replace(/Inhale when lowering/gi, "Inhale when lowering")
    .replace(/Exhale when pushing up/gi, "Exhale when pushing up")
    .replace(/Exhale when pulling up/gi, "Exhale when pulling up");
  
  return cleaned;
}

async function fixRemainingVietnamese() {
  try {
    console.log('Loading exercises.json...');
    const exercisesPath = path.join(__dirname, '../src/data/exercises.json');
    const data = await fs.readFile(exercisesPath, 'utf8');
    const exercises = JSON.parse(data);
    
    console.log(`Processing ${exercises.length} exercises...`);
    
    let fixedCount = 0;
    
    const cleanedExercises = exercises.map((exercise, index) => {
      let hasChanges = false;
      
      // Clean up common mistakes
      const cleanedMistakes = exercise.commonMistakes ? exercise.commonMistakes.map(mistake => {
        const cleaned = cleanupTranslation(mistake);
        if (cleaned !== mistake) hasChanges = true;
        return cleaned;
      }) : [];
      
      // Clean up breathing technique
      const cleanedBreathing = exercise.breathingTechnique ? cleanupTranslation(exercise.breathingTechnique) : '';
      if (cleanedBreathing !== exercise.breathingTechnique) hasChanges = true;
      
      // Clean up tips
      const cleanedTips = exercise.tips ? exercise.tips.map(tip => {
        const cleaned = cleanupTranslation(tip);
        if (cleaned !== tip) hasChanges = true;
        return cleaned;
      }) : [];
      
      // Clean up instructions
      const cleanedInstructions = exercise.instructions ? exercise.instructions.map(instruction => {
        const cleaned = cleanupTranslation(instruction);
        if (cleaned !== instruction) hasChanges = true;
        return cleaned;
      }) : [];
      
      if (hasChanges) {
        fixedCount++;
        console.log(`Fixed exercise ${index + 1}: ${exercise.name}`);
      }
      
      return {
        ...exercise,
        commonMistakes: cleanedMistakes,
        breathingTechnique: cleanedBreathing,
        tips: cleanedTips,
        instructions: cleanedInstructions
      };
    });
    
    // Save the cleaned exercises
    await fs.writeFile(exercisesPath, JSON.stringify(cleanedExercises, null, 2), 'utf8');
    
    console.log(`\n✓ Fixed ${fixedCount} exercises with remaining Vietnamese text`);
    console.log(`✓ Updated exercises.json successfully`);
    
  } catch (error) {
    console.error('Error fixing remaining Vietnamese text:', error);
  }
}

// Run the fix
fixRemainingVietnamese();
